name: Test DASHBOARD

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

# Controls when the action will run. Triggers the workflow on push or pull request
on:
  workflow_dispatch:
    inputs:
      ee:
        description: 'use the ee version of worker'
        required: false
        default: true
        type: boolean
  workflow_call:
    inputs:
      ee:
        description: 'use the ee version of worker'
        required: false
        default: false
        type: boolean

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  e2e_dashboard:
    strategy:
      fail-fast: false
      matrix:
        # run 4 copies of the current job in parallel
        containers: [1]
        total: [1]

    # The type of runner that the job will run on
    runs-on: blacksmith-8vcpu-ubuntu-2404
    timeout-minutes: 80

    permissions:
      contents: read
      packages: write
      deployments: write
      id-token: write

    steps:
      - id: determine_run_type
        name: Determing community vs enterprise run
        run: |
          if ! [[ -z "${{ secrets.SUBMODULES_TOKEN }}" ]]; then
            echo "enterprise_run=true" >> $GITHUB_OUTPUT
          else
            echo "enterprise_run=false" >> $GITHUB_OUTPUT
          fi

      - id: checkout-enterprise-code
        name: Checkout enterprise code from the submodule
        uses: actions/checkout@v4
        if: steps.determine_run_type.outputs.enterprise_run == 'true'
        with:
          submodules: true
          token: ${{ secrets.SUBMODULES_TOKEN }}

      - id: checkout-community-code
        name: Checkout community code
        uses: actions/checkout@v4
        if: steps.determine_run_type.outputs.enterprise_run != 'true'

      - uses: ./.github/actions/setup-project
        with:
          submodules: true

      - name: Create .env file for the Dashboard app
        working-directory: apps/dashboard
        run: |
          touch .env
          echo VITE_LAUNCH_DARKLY_CLIENT_SIDE_ID=${{ secrets.LAUNCH_DARKLY_CLIENT_SIDE_ID }} >> .env
          echo VITE_API_HOSTNAME=http://127.0.0.1:1336 >> .env
          echo VITE_WEBSOCKET_HOSTNAME=http://127.0.0.1:1340 >> .env
          echo VITE_LEGACY_DASHBOARD_URL=http://127.0.0.1:4200 >> .env
          echo VITE_CLERK_PUBLISHABLE_KEY=${{ secrets.CLERK_E2E_PUBLISHABLE_KEY }} >> .env

      - name: Create .env file for the Playwright
        working-directory: apps/dashboard
        run: |
          touch .env.playwright
          echo NOVU_ENTERPRISE=true >> .env.playwright
          echo NEW_RELIC_ENABLED=false >> .env.playwright
          echo NEW_RELIC_APP_NAME=Novu >> .env.playwright
          echo MONGO_URL=mongodb://127.0.0.1:27017/novu-test >> .env.playwright
          echo API_URL=http://127.0.0.1:1336 >> .env.playwright
          echo CLERK_ENABLED=true >> .env.playwright
          echo CLERK_PUBLISHABLE_KEY=${{ secrets.CLERK_E2E_PUBLISHABLE_KEY }} >> .env.playwright
          echo CLERK_SECRET_KEY=${{ secrets.CLERK_E2E_SECRET_KEY }} >> .env.playwright
          echo NODE_ENV=test >> .env.playwright

      - uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: build
          projects: '@novu/dashboard,@novu/api-service,@novu/worker'

      - uses: ./.github/actions/start-localstack
      - uses: ./.github/actions/setup-redis-cluster

      - name: Start API in TEST
        env:
          CI_EE_TEST: true
          CLERK_ENABLED: true
          CLERK_ISSUER_URL: https://neat-mole-83.clerk.accounts.dev
          CLERK_SECRET_KEY: ${{ secrets.CLERK_E2E_SECRET_KEY }}
        run: |
          cd apps/api && pnpm start:test &

      - name: Start Worker
        shell: bash
        env:
          NODE_ENV: 'test'
          PORT: '1342'
          CI_EE_TEST: true
        run: cd apps/worker && pnpm start:test &

      - name: Wait on API and Worker
        shell: bash
        run: wait-on --timeout=180000 http://127.0.0.1:1336/v1/health-check http://127.0.0.1:1342/v1/health-check

      - name: Start WS
        run: |
          cd apps/ws && pnpm start:test &

      - name: Wait on Services
        run: wait-on --timeout=180000 http://127.0.0.1:1340/v1/health-check

      - name: Get Playwright version
        working-directory: apps/dashboard
        id: playwright-version
        run: echo "version=$(pnpm list @playwright/test --depth=0 --json | jq -r '.[] | .dependencies["@playwright/test"].version')" >> $GITHUB_OUTPUT

      - name: Cache Playwright browsers
        uses: useblacksmith/cache@v5
        id: playwright-cache
        with:
          path: ~/.cache/ms-playwright
          key: playwright-browsers-${{ runner.os }}-${{ steps.playwright-version.outputs.version }}
          restore-keys: |
            playwright-browsers-${{ runner.os }}-

      - name: Install Playwright
        working-directory: apps/dashboard
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        run: pnpm test:e2e:install

      - name: Install Playwright (cache hit - verify browsers)
        working-directory: apps/dashboard
        if: steps.playwright-cache.outputs.cache-hit == 'true'
        run: pnpm test:e2e:install --dry-run || pnpm test:e2e:install

      - name: Run E2E tests
        working-directory: apps/dashboard
        env:
          NOVU_ENTERPRISE: ${{ steps.determine_run_type.outputs.enterprise_run }}
        run: pnpm test:e2e --shard=${{ matrix.containers }}/${{ matrix.total }}

      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: dashboard-blob-report-${{ matrix.containers }}
          path: apps/dashboard/blob-report
          retention-days: 1

  merge-reports:
    # Merge reports after playwright-tests, even if some shards have failed
    if: ${{ !cancelled() }}
    needs: [e2e_dashboard]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20.19.0

      - name: Download blob reports from GitHub Actions Artifacts
        uses: actions/download-artifact@v4
        with:
          path: dashboard-all-blob-reports
          pattern: dashboard-blob-report-*
          merge-multiple: true

      - name: Merge into HTML Report
        run: npx playwright merge-reports --reporter html ./dashboard-all-blob-reports

      - name: Upload HTML report
        uses: actions/upload-artifact@v4
        with:
          name: dashboard-html-report--attempt-${{ github.run_attempt }}
          path: playwright-report
          retention-days: 14

      - name: Send Slack notifications
        uses: ./.github/actions/slack-notify-on-failure
        if: failure()
        with:
          slackWebhookURL: ${{ secrets.SLACK_WEBHOOK_URL_ENG_FEED_GITHUB }}
