name: Assign milestone to PR

on:
  pull_request_review:
    types: [submitted]

concurrency:
  group: '${{ github.workflow }}-${{ github.ref }}'
  cancel-in-progress: true

jobs:
  assign_milestone:
    if: false
    name: Add milestone
    permissions:
      issues: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: useblacksmith/setup-node@v5
        with:
          node-version: 20.19.0
      - name: Install Octokit
        run: npm --prefix .github/workflows/scripts install @octokit/action

      - name: Assign milestones
        id: check
        run: node .github/workflows/scripts/milestone-assigner.js
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
