name: Publish NPM Packages Previews

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

on:
  workflow_dispatch:
  push:
    branches:
      - '*'
      - '!prod'

jobs:
  publish_preview_packages:
    runs-on: blacksmith-4vcpu-ubuntu-2404

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # https://vercel.com/guides/corepack-errors-github-actions
      - name: Use Latest Corepack
        run: |
          npm install -g corepack@latest
          corepack enable

      - uses: useblacksmith/setup-node@v5
        with:
          node-version: 20.19.0
          cache: 'pnpm'

      - name: Install pnpm
        run: corepack enable

      - name: Install dependencies
        run: pnpm install

      - name: Teach Novu preview packages to work with latest dependencies
        run: pnpm run packages:set-latest

      - name: Build
        run: pnpm run preview:pkg:build

      - name: Release package previews to pkg.pr.new
        run: pnpm run preview:pkg:publish
        if: ${{ success() }}
