name: Add Triage Label

on:
  issues:
    types: [opened]

concurrency:
  group: '${{ github.workflow }}-${{ github.ref }}'
  cancel-in-progress: true

jobs:
  check:
    name: Verify
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 20.19.0
      - name: Install Octokit
        run: npm --prefix .github/workflows/scripts install @octokit/action@6

      - name: Check if user is a community contributor
        id: check
        run: node .github/workflows/scripts/add-triage-label.js
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
