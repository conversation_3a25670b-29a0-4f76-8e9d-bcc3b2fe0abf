name: Release Packages

on:
  workflow_dispatch:
    inputs:
      packages:
        description: 'Packages to release (comma-separated)'
        required: true
        type: string
        default: '@novu/js,@novu/react,@novu/nextjs,@novu/react-native'
      version:
        description: 'Version to release (e.g., v3.0.0)'
        required: true
        type: string
        default: 'v3.0.0'
      previous_tag:
        description: 'Previous tag to generate changelog from (e.g., @novu/js@v3.4.0)'
        required: true
        type: string
        default: '@novu/js@v3.4.0'
      nightly:
        description: 'Publish as nightly release'
        required: false
        type: boolean
        default: false

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.11.0
          run_install: false

      - name: Setup Node Version
        uses: actions/setup-node@v4
        with:
          node-version: '20.19.0'
          cache: 'pnpm'
          registry-url: 'https://registry.npmjs.org'

      - name: Install Dependencies
        shell: bash
        run: |
          pnpm install --frozen-lockfile
          pnpm nx --version
          pnpm list nx

      - name: Set version for nightly
        if: ${{ github.event.inputs.nightly }}
        run: |
          DATE=$(date +'%Y%m%d')
          echo "NIGHTLY_VERSION=${{ github.event.inputs.version }}-nightly.${DATE}" >> $GITHUB_ENV
          echo "Using nightly version: $NIGHTLY_VERSION"

      - name: Release version
        run: |
          if [ "${{ github.event.inputs.nightly }}" = "true" ]; then
            echo "Running nightly release with version: ${{ env.NIGHTLY_VERSION }}"
            pnpm nx release version ${{ env.NIGHTLY_VERSION }} --projects=${{ github.event.inputs.packages }} --preid nightly --verbose
          else
            echo "Running regular release with version: ${{ github.event.inputs.version }}"
            pnpm nx release version ${{ github.event.inputs.version }} --projects=${{ github.event.inputs.packages }} --verbose
          fi

      - name: Configure Git
        run: |
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"

      - name: Generate changelog
        run: |
          if [ "${{ github.event.inputs.nightly }}" = "true" ]; then
            pnpm nx release changelog ${{ env.NIGHTLY_VERSION }} --projects=${{ github.event.inputs.packages }} --from=${{ github.event.inputs.previous_tag }}
          else
            pnpm nx release changelog ${{ github.event.inputs.version }} --projects=${{ github.event.inputs.packages }} --from=${{ github.event.inputs.previous_tag }}
          fi

      - name: Build packages
        run: pnpm run build:packages

      - name: Publish packages
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: |
          if [ "${{ github.event.inputs.nightly }}" = "true" ]; then
            pnpm nx release publish --projects=${{ github.event.inputs.packages }} --tag nightly
          else
            pnpm nx release publish --projects=${{ github.event.inputs.packages }}
          fi

      - name: Create Pull Request
        if: ${{ !github.event.inputs.nightly }}
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "chore: release ${{ github.event.inputs.version }}"
          title: "Release ${{ github.event.inputs.version }}"
          body: |
            This PR contains the release changes for version ${{ github.event.inputs.version }}
            
            Packages released:
            ${{ github.event.inputs.packages }}
          branch: release-${{ github.event.inputs.version }}
          base: next 
