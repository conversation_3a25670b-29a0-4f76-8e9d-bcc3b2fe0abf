name: Tag Docker Version

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  workflow_dispatch:
    inputs:
      version:
        description: 'The version to tag docker images'
        required: true
        type: string

jobs:
  tag_images:
    runs-on: blacksmith-4vcpu-ubuntu-2404
    timeout-minutes: 80
    environment: Production
    permissions:
      contents: read
      packages: write
      deployments: write
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v4
      - name: Setup kernel for react native, increase watchers
        run: echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf && sudo sysctl -p
      - uses: useblacksmith/setup-node@v5
        with:
          node-version: '20.19.0'

      - name: Login to docker
        env:
          GH_ACTOR: ${{ github.actor }}
          GH_PASSWORD: ${{ secrets.GH_PACKAGES }}
        run: |
          echo $GH_PASSWORD | docker login ghcr.io -u $GH_ACTOR --password-stdin

      - name: Tag API
        env:
          REGISTRY_OWNER: novuhq
          DOCKER_NAME: novu/api
          IMAGE_TAG: ${{ github.sha }}
          GH_ACTOR: ${{ github.actor }}
          GH_PASSWORD: ${{ secrets.GH_PACKAGES }}
          DOCKER_VERSION: ${{ inputs.version }}
        run: |
          docker pull ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod
          docker tag ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION
          docker push ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION

      - name: Tag Worker
        env:
          REGISTRY_OWNER: novuhq
          DOCKER_NAME: novu/worker
          IMAGE_TAG: ${{ github.sha }}
          GH_ACTOR: ${{ github.actor }}
          GH_PASSWORD: ${{ secrets.GH_PACKAGES }}
          DOCKER_VERSION: ${{ inputs.version }}
        run: |
          docker pull ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod
          docker tag ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION
          docker push ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION

      - name: Tag WS
        env:
          REGISTRY_OWNER: novuhq
          DOCKER_NAME: novu/ws
          IMAGE_TAG: ${{ github.sha }}
          GH_ACTOR: ${{ github.actor }}
          GH_PASSWORD: ${{ secrets.GH_PACKAGES }}
          DOCKER_VERSION: ${{ inputs.version }}
        run: |
          docker pull ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod
          docker tag ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION
          docker push ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION

      - name: Tag EMBED
        env:
          REGISTRY_OWNER: novuhq
          DOCKER_NAME: novu/embed
          IMAGE_TAG: ${{ github.sha }}
          GH_ACTOR: ${{ github.actor }}
          GH_PASSWORD: ${{ secrets.GH_PACKAGES }}
          DOCKER_VERSION: ${{ inputs.version }}
        run: |
          docker pull ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod
          docker tag ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION
          docker push ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION

      - name: Tag WIDGET
        env:
          REGISTRY_OWNER: novuhq
          DOCKER_NAME: novu/widget
          IMAGE_TAG: ${{ github.sha }}
          GH_ACTOR: ${{ github.actor }}
          GH_PASSWORD: ${{ secrets.GH_PACKAGES }}
          DOCKER_VERSION: ${{ inputs.version }}
        run: |
          docker pull ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod
          docker tag ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION
          docker push ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION

      - name: Tag WEB
        env:
          REGISTRY_OWNER: novuhq
          DOCKER_NAME: novu/web
          IMAGE_TAG: ${{ github.sha }}
          GH_ACTOR: ${{ github.actor }}
          GH_PASSWORD: ${{ secrets.GH_PACKAGES }}
          DOCKER_VERSION: ${{ inputs.version }}
        run: |
          docker pull ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod
          docker tag ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION
          docker push ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION

      - name: Tag SDK
        env:
          REGISTRY_OWNER: novuhq
          DOCKER_NAME: novu/sdk
          IMAGE_TAG: ${{ github.sha }}
          GH_ACTOR: ${{ github.actor }}
          GH_PASSWORD: ${{ secrets.GH_PACKAGES }}
          DOCKER_VERSION: ${{ inputs.version }}
        run: |
          docker pull ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod
          docker tag ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:prod ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION
          docker push ghcr.io/$REGISTRY_OWNER/$DOCKER_NAME:$DOCKER_VERSION
