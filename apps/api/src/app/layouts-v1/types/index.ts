import {
  ChannelTypeEnum,
  EnvironmentId,
  IEmailBlock,
  ITemplateVariable,
  LayoutDescription,
  LayoutIdentifier,
  LayoutId,
  LayoutName,
  OrderDirectionEnum,
  OrganizationId,
  TemplateVariableTypeEnum,
  UserId,
} from '@novu/shared';

export type LayoutVariables = ITemplateVariable[];

export {
  ChannelTypeEnum,
  EnvironmentId,
  IEmailBlock,
  ITemplateVariable,
  OrderDirectionEnum,
  OrganizationId,
  LayoutDescription,
  LayoutId,
  LayoutName,
  LayoutIdentifier,
  TemplateVariableTypeEnum,
  UserId,
};
