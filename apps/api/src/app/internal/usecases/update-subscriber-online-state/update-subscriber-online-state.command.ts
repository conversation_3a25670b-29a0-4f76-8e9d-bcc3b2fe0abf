import { IsString, IsNotEmpty, IsBoolean, IsO<PERSON>al, IsMongoId } from 'class-validator';
import { BaseCommand } from '@novu/application-generic';

export class UpdateSubscriberOnlineStateCommand extends BaseCommand {
  @IsString()
  @IsNotEmpty()
  subscriberId: string;

  @IsString()
  @IsNotEmpty()
  @IsMongoId()
  environmentId: string;

  @IsBoolean()
  isOnline: boolean;

  @IsOptional()
  timestamp?: number;
}
