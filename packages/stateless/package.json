{"name": "@novu/stateless", "version": "2.6.6", "description": "Notification Management Framework", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/cjs/index.d.ts", "files": ["dist/", "!**/*.spec.*", "!**/*.json", "CHANGELOG.md", "LICENSE", "README.md"], "repository": "https://github.com/novuhq/novu", "license": "MIT", "keywords": [], "private": false, "scripts": {"start": "npm run start:dev", "start:dev": "npm run watch:build", "prebuild": "<PERSON><PERSON><PERSON> build", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc -p tsconfig.json", "build:esm": "tsc -p tsconfig.esm.json", "fix": "run-s fix:*", "fix:prettier": "prettier \"src/**/*.ts\" --write", "lint:fix": "eslint src --fix", "test": "run-s test:*", "lint": "eslint src", "test:prettier": "prettier \"src/**/*.ts\"", "test:unit": "jest src", "check-cli": "run-s test diff-integration-tests check-integration-tests", "check-integration-tests": "run-s check-integration-test:*", "diff-integration-tests": "mkdir -p diff && rm -rf diff/test && cp -r test diff/test && rm -rf diff/test/test-*/.git && cd diff && git init --quiet && git add -A && git commit --quiet --no-verify --allow-empty -m 'WIP' && echo '\\n\\nCommitted most recent integration test output in the \"diff\" directory. Review the changes with \"cd diff && git diff HEAD\" or your preferred git diff viewer.'", "watch:build": "tsc -p tsconfig.json -w", "watch:test": "jest src --watch", "doc": "run-s doc:html && open-cli build/docs/index.html", "doc:html": "typedoc src/ --exclude **/*.spec.ts --target ES6 --mode file --out build/docs", "doc:json": "typedoc src/ --exclude **/*.spec.ts --target ES6 --mode file --json build/docs/typedoc.json", "doc:publish": "gh-pages -m \"[ci skip] Updates\" -d build/docs", "reset-hard": "git clean -dfx && git reset --hard && pnpm install", "prepare-release": "run-s reset-hard test"}, "publishConfig": {"access": "public"}, "engines": {"node": ">=10"}, "dependencies": {"handlebars": "^4.7.7", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2"}, "devDependencies": {"@types/jest": "29.5.2", "@types/lodash.get": "^4.4.6", "@types/lodash.merge": "^4.6.6", "@types/node": "^20.15.0", "codecov": "^3.5.0", "jest": "^29.7.0", "npm-run-all": "^4.1.5", "open-cli": "^6.0.1", "rimraf": "^3.0.2", "run-p": "0.0.0", "ts-jest": "^29.1.0", "typedoc": "^0.24.0", "typescript": "5.6.2"}, "prettier": {"singleQuote": true}, "nx": {"tags": ["type:package"]}}